RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING
======================================================================

MODELO BINÁRIO:
  • Tipo: XGBoost Binário
  • Classes: 0=Venda, 1=Compra
  • Função de perda: logloss
  • Threshold de probabilidade: 0.5 (sinais só gerados se prob > 0.5)
  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade
  • Features econométricas: Parkinson, MFI, EMV, Am<PERSON>ud, Roll Spread,
    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (11 features)
  • Features lagged: 5 lags para cada feature econométrica
  • Total de features: 115
  • Acurácia geral: 0.666

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 15y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Lags features econométricas: 5
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (115):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Volume
  12. Spread
  13. Volatilidade
  14. Segunda
  15. Terca
  16. Quarta
  17. Quinta
  18. Sexta
  19. Mes_1
  20. Mes_2
  21. Mes_3
  22. Mes_4
  23. Mes_5
  24. Mes_6
  25. Mes_7
  26. Mes_8
  27. Mes_9
  28. Mes_10
  29. Mes_11
  30. Mes_12
  31. Quarter_1
  32. Quarter_2
  33. Quarter_3
  34. Quarter_4
  35. Parkinson_Volatility
  36. MFI
  37. EMV
  38. EMV_MA
  39. Amihud
  40. Roll_Spread
  41. Hurst
  42. Vol_per_Volume
  43. CMF
  44. AD_Line
  45. VO
  46. Volume_Lag_1
  47. Volume_Lag_2
  48. Volume_Lag_3
  49. Volume_Lag_4
  50. Volume_Lag_5
  51. Spread_Lag_1
  52. Spread_Lag_2
  53. Spread_Lag_3
  54. Spread_Lag_4
  55. Spread_Lag_5
  56. Volatilidade_Lag_1
  57. Volatilidade_Lag_2
  58. Volatilidade_Lag_3
  59. Volatilidade_Lag_4
  60. Volatilidade_Lag_5
  61. Parkinson_Volatility_Lag_1
  62. Parkinson_Volatility_Lag_2
  63. Parkinson_Volatility_Lag_3
  64. Parkinson_Volatility_Lag_4
  65. Parkinson_Volatility_Lag_5
  66. MFI_Lag_1
  67. MFI_Lag_2
  68. MFI_Lag_3
  69. MFI_Lag_4
  70. MFI_Lag_5
  71. EMV_Lag_1
  72. EMV_Lag_2
  73. EMV_Lag_3
  74. EMV_Lag_4
  75. EMV_Lag_5
  76. EMV_MA_Lag_1
  77. EMV_MA_Lag_2
  78. EMV_MA_Lag_3
  79. EMV_MA_Lag_4
  80. EMV_MA_Lag_5
  81. Amihud_Lag_1
  82. Amihud_Lag_2
  83. Amihud_Lag_3
  84. Amihud_Lag_4
  85. Amihud_Lag_5
  86. Roll_Spread_Lag_1
  87. Roll_Spread_Lag_2
  88. Roll_Spread_Lag_3
  89. Roll_Spread_Lag_4
  90. Roll_Spread_Lag_5
  91. Hurst_Lag_1
  92. Hurst_Lag_2
  93. Hurst_Lag_3
  94. Hurst_Lag_4
  95. Hurst_Lag_5
  96. Vol_per_Volume_Lag_1
  97. Vol_per_Volume_Lag_2
  98. Vol_per_Volume_Lag_3
  99. Vol_per_Volume_Lag_4
  100. Vol_per_Volume_Lag_5
  101. CMF_Lag_1
  102. CMF_Lag_2
  103. CMF_Lag_3
  104. CMF_Lag_4
  105. CMF_Lag_5
  106. AD_Line_Lag_1
  107. AD_Line_Lag_2
  108. AD_Line_Lag_3
  109. AD_Line_Lag_4
  110. AD_Line_Lag_5
  111. VO_Lag_1
  112. VO_Lag_2
  113. VO_Lag_3
  114. VO_Lag_4
  115. VO_Lag_5

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.666
  • Distribuição das Predições:
    - Venda: 7916 (48.9%)
    - Compra: 8269 (51.1%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
