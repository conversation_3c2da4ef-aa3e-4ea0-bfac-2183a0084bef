RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING
======================================================================

MODELO BINÁRIO:
  • Tipo: XGBoost Binário
  • Classes: 0=Venda, 1=Compra
  • Função de perda: logloss
  • Threshold de probabilidade: 0.5 (sinais só gerados se prob > 0.5)
  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade
  • Features econométricas: Parkinson, MFI, EMV, Am<PERSON>ud, Roll Spread,
    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (11 features)
  • Features lagged: 5 lags para cada feature econométrica
  • Total de features: 111
  • Acurácia geral: 0.668

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 15y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Lags features econométricas: 5
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (111):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Volume
  12. Spread
  13. Volatilidade
  14. Segunda
  15. Terca
  16. Quarta
  17. Quinta
  18. Sexta
  19. Mes_1
  20. Mes_2
  21. Mes_3
  22. Mes_4
  23. Mes_5
  24. Mes_6
  25. Mes_7
  26. Mes_8
  27. Mes_9
  28. Mes_10
  29. Mes_11
  30. Mes_12
  31. Parkinson_Volatility
  32. MFI
  33. EMV
  34. EMV_MA
  35. Amihud
  36. Roll_Spread
  37. Hurst
  38. Vol_per_Volume
  39. CMF
  40. AD_Line
  41. VO
  42. Volume_Lag_1
  43. Volume_Lag_2
  44. Volume_Lag_3
  45. Volume_Lag_4
  46. Volume_Lag_5
  47. Spread_Lag_1
  48. Spread_Lag_2
  49. Spread_Lag_3
  50. Spread_Lag_4
  51. Spread_Lag_5
  52. Volatilidade_Lag_1
  53. Volatilidade_Lag_2
  54. Volatilidade_Lag_3
  55. Volatilidade_Lag_4
  56. Volatilidade_Lag_5
  57. Parkinson_Volatility_Lag_1
  58. Parkinson_Volatility_Lag_2
  59. Parkinson_Volatility_Lag_3
  60. Parkinson_Volatility_Lag_4
  61. Parkinson_Volatility_Lag_5
  62. MFI_Lag_1
  63. MFI_Lag_2
  64. MFI_Lag_3
  65. MFI_Lag_4
  66. MFI_Lag_5
  67. EMV_Lag_1
  68. EMV_Lag_2
  69. EMV_Lag_3
  70. EMV_Lag_4
  71. EMV_Lag_5
  72. EMV_MA_Lag_1
  73. EMV_MA_Lag_2
  74. EMV_MA_Lag_3
  75. EMV_MA_Lag_4
  76. EMV_MA_Lag_5
  77. Amihud_Lag_1
  78. Amihud_Lag_2
  79. Amihud_Lag_3
  80. Amihud_Lag_4
  81. Amihud_Lag_5
  82. Roll_Spread_Lag_1
  83. Roll_Spread_Lag_2
  84. Roll_Spread_Lag_3
  85. Roll_Spread_Lag_4
  86. Roll_Spread_Lag_5
  87. Hurst_Lag_1
  88. Hurst_Lag_2
  89. Hurst_Lag_3
  90. Hurst_Lag_4
  91. Hurst_Lag_5
  92. Vol_per_Volume_Lag_1
  93. Vol_per_Volume_Lag_2
  94. Vol_per_Volume_Lag_3
  95. Vol_per_Volume_Lag_4
  96. Vol_per_Volume_Lag_5
  97. CMF_Lag_1
  98. CMF_Lag_2
  99. CMF_Lag_3
  100. CMF_Lag_4
  101. CMF_Lag_5
  102. AD_Line_Lag_1
  103. AD_Line_Lag_2
  104. AD_Line_Lag_3
  105. AD_Line_Lag_4
  106. AD_Line_Lag_5
  107. VO_Lag_1
  108. VO_Lag_2
  109. VO_Lag_3
  110. VO_Lag_4
  111. VO_Lag_5

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.668
  • Distribuição das Predições:
    - Venda: 7801 (48.2%)
    - Compra: 8395 (51.8%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
